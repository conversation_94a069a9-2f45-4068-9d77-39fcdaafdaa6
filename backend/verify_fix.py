#!/usr/bin/env python3
"""
验证Moonshot修复的简单脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

def verify_configuration():
    """验证配置是否正确加载"""
    try:
        print("🔍 验证修复内容...")

        print("\n🔍 验证模型配置代码...")

        # 检查llms.py文件内容
        with open('utils/llms.py', 'r', encoding='utf-8') as f:
            llms_content = f.read()

        # 检查关键配置
        checks = [
            ('moonshot-v1-32k', 'Moonshot模型版本升级'),
            ('max_tokens": 16000', 'Max tokens设置'),
            ('temperature": 0.7', 'Temperature设置'),
            ('frequency_penalty": 0.1', 'Frequency penalty设置'),
        ]

        for check_str, description in checks:
            if check_str in llms_content:
                print(f"✅ {description}: 已配置")
            else:
                print(f"❌ {description}: 未找到")

        print("\n🔍 验证AI服务代码...")

        # 检查ai_service.py文件
        with open('services/ai_service.py', 'r', encoding='utf-8') as f:
            ai_service_content = f.read()

        service_checks = [
            ('_extract_test_case_count', '数量提取功能'),
            ('_enhance_prompt_for_completeness', '完整性增强功能'),
            ('必须完整生成所有测试用例', '完整性提示词'),
        ]

        for check_str, description in service_checks:
            if check_str in ai_service_content:
                print(f"✅ {description}: 已添加")
            else:
                print(f"❌ {description}: 未找到")

        print("\n🎉 代码验证完成！")
        return True

    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Moonshot修复验证脚本")
    print("=" * 50)
    
    success = verify_configuration()
    
    if success:
        print("\n✅ 验证成功！修复已正确应用。")
        print("\n📝 修复摘要:")
        print("   1. Moonshot模型升级到32k版本")
        print("   2. Max tokens增加到16000")
        print("   3. 添加了完整性增强提示词")
        print("   4. 优化了系统消息")
        print("   5. 添加了数量提取和验证功能")
        print("\n🚀 现在可以测试Moonshot模型的完整性了！")
    else:
        print("\n❌ 验证失败，请检查配置。")
    
    print("=" * 50)
