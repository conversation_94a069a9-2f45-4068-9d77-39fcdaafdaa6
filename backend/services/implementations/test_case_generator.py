"""
测试用例生成器实现
负责生成测试用例的核心逻辑
"""
from typing import List, Dict, Any, AsyncGenerator, Optional
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.messages import ModelClientStreamingChunkEvent

from ..interfaces import ITestCaseGenerator, IModelSelector, IPromptGenerator
from .file_processors import FileProcessorFactory
from utils.logging_config import get_logger

logger = get_logger(__name__)


class TestCaseGenerator(ITestCaseGenerator):
    """测试用例生成器实现"""
    
    def __init__(self, model_selector: IModelSelector, prompt_generator: IPromptGenerator):
        self._model_selector = model_selector
        self._prompt_generator = prompt_generator
        self._file_processor_factory = FileProcessorFactory()
    
    async def generate_test_cases_stream(
        self,
        file_path: str,
        context: str,
        requirements: str,
        preferred_model: Optional[str] = None
    ) -> AsyncGenerator[str, None]:
        """流式生成测试用例"""
        try:
            # 获取文件扩展名
            file_extension = file_path.lower().split('.')[-1] if '.' in file_path else ''
            
            # 选择合适的模型
            selected_model = self._model_selector.select_model_for_file(file_path, preferred_model)
            
            # 输出生成信息
            yield "# 正在生成测试用例...\n\n"
            yield f"**文件信息**\n"
            yield f"- 文件类型: {file_extension.upper() if file_extension else '未知'}\n"
            yield f"- 使用模型: {selected_model.get_model_name()}\n\n"
            yield "---\n\n"
            
            # 处理文件
            file_processor = self._file_processor_factory.get_processor(file_extension)
            processed_data = await file_processor.process_file(file_path)
            
            # 生成提示词和系统消息
            prompt, system_message = self._prepare_prompt_and_system_message(
                processed_data, context, requirements, file_extension
            )
            
            # 创建AI代理
            agent = AssistantAgent(
                name="test_case_agent",
                model_client=selected_model.client,
                system_message=system_message,
                model_client_stream=True,
            )
            
            # 流式输出生成的测试用例
            async for event in agent.run_stream(task=prompt):
                if isinstance(event, ModelClientStreamingChunkEvent):
                    yield event.content
                elif isinstance(event, TaskResult):
                    break
                    
        except Exception as e:
            logger.error(f"测试用例生成失败: {str(e)}")
            yield f"\n\n**错误**: 测试用例生成失败 - {str(e)}\n"
    
    def _prepare_prompt_and_system_message(
        self, 
        processed_data: Dict[str, Any], 
        context: str, 
        requirements: str, 
        file_extension: str
    ) -> tuple[str, str]:
        """准备提示词和系统消息"""
        data_type = processed_data.get('type', 'unknown')
        
        if data_type == 'image':
            # 图像文件处理
            prompt = self._prompt_generator.generate_prompt(
                file_extension, "", context, requirements
            )
            system_message = """你是一个专业的测试用例生成器，擅长基于图像内容生成全面的测试用例。

**核心任务**：根据用户提供的图像、上下文和需求，生成完整、详细的测试用例。

**重要指示**：
1. 必须完整生成所有测试用例，不要因为长度限制而截断
2. 每个测试用例都必须包含完整的信息
3. 严格按照指定的Markdown格式生成
4. 确保内容完整性，不要遗漏任何测试用例

请确保输出完整，不要因为任何原因截断内容。"""
            
        elif data_type == 'pdf':
            # PDF文件处理
            content = processed_data.get('content', '')
            prompt = self._prompt_generator.generate_prompt(
                file_extension, content, context, requirements
            )
            system_message = """你是一个专业的测试用例生成器，擅长基于文档内容生成全面的测试用例。

**核心任务**：根据用户提供的文档内容、上下文和需求，生成完整、详细的测试用例。

**重要指示**：
1. 必须完整生成所有测试用例，不要因为长度限制而截断
2. 每个测试用例都必须包含完整的信息
3. 严格按照标准Markdown格式生成测试用例
4. 确保内容完整性，不要遗漏任何测试用例

请确保输出完整，不要因为任何原因截断内容。"""
            
        elif data_type == 'openapi':
            # OpenAPI文件处理
            api_info = processed_data.get('api_info', {})
            test_scenarios = processed_data.get('test_scenarios', [])
            
            # 构建API信息内容
            content = self._format_api_content(api_info, test_scenarios)
            prompt = self._prompt_generator.generate_prompt(
                file_extension, content, context, requirements
            )
            system_message = """你是一个专业的API测试用例生成器，擅长基于OpenAPI文档生成全面的API测试用例。

**核心任务**：根据用户提供的API文档、上下文和需求，生成完整、详细的API测试用例。

**重要指示**：
1. 必须完整生成所有测试用例，不要因为长度限制而截断
2. 每个测试用例都必须包含完整的信息
3. 严格按照标准Markdown格式生成测试用例
4. 确保内容完整性，不要遗漏任何测试用例
5. 特别关注API的各种测试场景（正向、负向、边界值等）

请确保输出完整，不要因为任何原因截断内容。"""
            
        else:
            # 默认处理
            content = processed_data.get('content', '')
            prompt = self._prompt_generator.generate_prompt(
                file_extension, content, context, requirements
            )
            system_message = """你是一个专业的测试用例生成器，擅长基于文档内容生成全面的测试用例。

**核心任务**：根据用户提供的文档内容、上下文和需求，生成完整、详细的测试用例。

**重要指示**：
1. 必须完整生成所有测试用例，不要因为长度限制而截断
2. 每个测试用例都必须包含完整的信息
3. 严格按照标准Markdown格式生成测试用例
4. 如果用户需求中指定了数量，请确保生成相应数量的测试用例
5. 确保内容完整性，不要遗漏任何测试用例

请确保输出完整，不要因为任何原因截断内容。"""
        
        return prompt, system_message
    
    def _format_api_content(self, api_info: Dict[str, Any], test_scenarios: List[Dict[str, Any]]) -> str:
        """格式化API内容"""
        content_parts = []

        # API基本信息
        info = api_info.get('info', {})
        if info.get('title'):
            content_parts.append(f"API名称: {info['title']}")
        if info.get('version'):
            content_parts.append(f"版本: {info['version']}")
        if info.get('description'):
            content_parts.append(f"描述: {info['description']}")

        # 服务器信息
        if api_info.get('servers'):
            content_parts.append("\n服务器:")
            for server in api_info['servers']:
                content_parts.append(f"- {server.get('url', '')} - {server.get('description', '')}")

        # API端点
        if api_info.get('paths'):
            content_parts.append("\nAPI端点:")
            # 处理OpenAPI服务返回的路径格式（列表格式）
            for path_item in api_info['paths']:
                path = path_item.get('path', '')
                operations = path_item.get('operations', [])
                for operation in operations:
                    method = operation.get('method', '')
                    summary = operation.get('summary', '')
                    content_parts.append(f"- {method} {path}: {summary}")

        # 测试场景
        if test_scenarios:
            content_parts.append("\n推荐测试场景:")
            for i, scenario in enumerate(test_scenarios, 1):
                content_parts.append(f"{i}. {scenario.get('description', '')}")

        return "\n".join(content_parts)
    
    def generate_mindmap_from_test_cases(self, test_cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """从测试用例生成思维导图数据"""
        if not test_cases:
            return {"name": "测试用例", "children": []}
        
        # 创建根节点
        mindmap = {
            "name": "测试用例总览",
            "children": []
        }
        
        # 按优先级分组
        priority_groups = {}
        for tc in test_cases:
            priority = tc.get('priority', 'Medium')
            if priority not in priority_groups:
                priority_groups[priority] = []
            priority_groups[priority].append(tc)
        
        # 为每个优先级创建分支
        for priority, cases in priority_groups.items():
            priority_node = {
                "name": f"{priority} 优先级 ({len(cases)}个)",
                "children": []
            }
            
            for tc in cases:
                tc_node = {
                    "name": f"{tc.get('id', 'TC-???')}: {tc.get('title', '未命名')}",
                    "children": []
                }
                
                # 添加描述
                if tc.get('description'):
                    tc_node["children"].append({
                        "name": f"描述: {tc['description'][:50]}..."
                    })
                
                # 添加步骤
                steps = tc.get('steps', [])
                if steps:
                    steps_node = {
                        "name": f"测试步骤 ({len(steps)}步)",
                        "children": []
                    }
                    for step in steps[:5]:  # 最多显示5个步骤
                        steps_node["children"].append({
                            "name": f"步骤{step.get('step_number', '?')}: {step.get('description', '')[:30]}..."
                        })
                    tc_node["children"].append(steps_node)
                
                priority_node["children"].append(tc_node)
            
            mindmap["children"].append(priority_node)
        
        return mindmap
