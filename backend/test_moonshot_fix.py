#!/usr/bin/env python3
"""
测试Moonshot模型修复效果的脚本
用于验证Moonshot模型是否能够按要求生成完整的测试用例
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from services.ai_service import AIService
from utils.logging_config import get_logger

logger = get_logger(__name__)


async def test_moonshot_completeness():
    """测试Moonshot模型的内容完整性"""
    
    # 创建AI服务实例
    ai_service = AIService()
    
    # 测试用例1：明确指定数量的需求
    test_context = "这是一个课程表管理系统"
    test_requirements = "生成13条测试用例，包括正向和负向测试场景"
    
    print("=" * 60)
    print("测试Moonshot模型内容完整性")
    print("=" * 60)
    print(f"上下文: {test_context}")
    print(f"需求: {test_requirements}")
    print("=" * 60)
    
    # 创建一个简单的测试文件
    test_file_path = "test_document.txt"
    with open(test_file_path, "w", encoding="utf-8") as f:
        f.write("""
课程表管理系统功能说明

主要功能：
1. 课程添加 - 用户可以添加新的课程信息
2. 课程查看 - 用户可以查看已有的课程列表
3. 课程编辑 - 用户可以修改课程信息
4. 课程删除 - 用户可以删除不需要的课程
5. 时间冲突检测 - 系统自动检测课程时间冲突
6. 课程搜索 - 用户可以搜索特定课程
7. 导出功能 - 用户可以导出课程表
        """)
    
    try:
        # 测试Moonshot模型
        print("\n开始测试Moonshot模型...")
        test_case_count = 0
        generated_content = ""
        
        async for chunk in ai_service.generate_test_cases_stream(
            file_path=test_file_path,
            context=test_context,
            requirements=test_requirements,
            preferred_model="moonshot"
        ):
            generated_content += chunk
            print(chunk, end="", flush=True)
            
            # 计算生成的测试用例数量
            if "## TC-" in chunk:
                test_case_count += chunk.count("## TC-")
        
        print(f"\n\n{'='*60}")
        print(f"测试结果统计:")
        print(f"生成的测试用例数量: {test_case_count}")
        print(f"生成内容总长度: {len(generated_content)} 字符")
        print(f"是否包含完整的13条测试用例: {'是' if test_case_count >= 13 else '否'}")
        
        # 检查内容完整性
        if "TC-013:" in generated_content or test_case_count >= 13:
            print("✅ 内容完整性测试通过 - Moonshot能够生成完整的测试用例")
        else:
            print("❌ 内容完整性测试失败 - Moonshot未能生成完整的测试用例")
            print(f"   预期: 13条测试用例")
            print(f"   实际: {test_case_count}条测试用例")
        
        print(f"{'='*60}")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        print(f"❌ 测试失败: {str(e)}")
    
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)


async def test_model_comparison():
    """比较不同模型的输出完整性"""
    
    ai_service = AIService()
    test_context = "这是一个课程表管理系统"
    test_requirements = "生成13条测试用例"
    
    # 创建测试文件
    test_file_path = "test_comparison.txt"
    with open(test_file_path, "w", encoding="utf-8") as f:
        f.write("课程表管理系统 - 用于管理学生课程安排的系统")
    
    models_to_test = ["deepseek", "moonshot"]
    results = {}
    
    try:
        for model in models_to_test:
            print(f"\n{'='*40}")
            print(f"测试模型: {model.upper()}")
            print(f"{'='*40}")
            
            test_case_count = 0
            content_length = 0
            
            async for chunk in ai_service.generate_test_cases_stream(
                file_path=test_file_path,
                context=test_context,
                requirements=test_requirements,
                preferred_model=model
            ):
                content_length += len(chunk)
                if "## TC-" in chunk:
                    test_case_count += chunk.count("## TC-")
                
                # 只显示前100个字符，避免输出过长
                if content_length <= 100:
                    print(chunk, end="", flush=True)
                elif content_length == 101:
                    print("...(输出已截断，仅用于测试)")
            
            results[model] = {
                "test_case_count": test_case_count,
                "content_length": content_length
            }
            
            print(f"\n模型 {model} 结果:")
            print(f"  - 测试用例数量: {test_case_count}")
            print(f"  - 内容长度: {content_length} 字符")
        
        # 比较结果
        print(f"\n{'='*50}")
        print("模型比较结果:")
        print(f"{'='*50}")
        
        for model, result in results.items():
            status = "✅ 完整" if result["test_case_count"] >= 13 else "❌ 不完整"
            print(f"{model.upper():10} | 测试用例: {result['test_case_count']:2d} | 长度: {result['content_length']:5d} | {status}")
        
        # 检查修复效果
        if results.get("moonshot", {}).get("test_case_count", 0) >= 13:
            print("\n🎉 修复成功！Moonshot现在能够生成完整的测试用例了！")
        else:
            print("\n⚠️  修复可能需要进一步调整")
    
    except Exception as e:
        logger.error(f"比较测试中发生错误: {str(e)}")
        print(f"❌ 比较测试失败: {str(e)}")
    
    finally:
        if os.path.exists(test_file_path):
            os.remove(test_file_path)


async def main():
    """主函数"""
    print("开始测试Moonshot模型修复效果...")
    
    # 测试1: 基本完整性测试
    await test_moonshot_completeness()
    
    # 等待一下
    await asyncio.sleep(2)
    
    # 测试2: 模型比较测试
    await test_model_comparison()
    
    print("\n测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
