import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import LinearProgress from '@mui/material/LinearProgress';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import Collapse from '@mui/material/Collapse';
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import PauseIcon from '@mui/icons-material/Pause';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { StreamingSkeleton } from './LoadingSkeleton';

const StreamingOutput = React.memo(({
  content,
  isStreaming = false,
  onPause,
  onResume,
  isPaused = false,
  showProgress = true,
  maxHeight = '500px'
}) => {
  const outputRef = useRef(null);
  const [isExpanded, setIsExpanded] = useState(true);
  const [autoScroll, setAutoScroll] = useState(true);
  const [showCopySuccess, setShowCopySuccess] = useState(false);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const scrollTimeoutRef = useRef(null);

  // 优化的内容处理 - 避免频繁重新渲染
  const processedContent = useMemo(() => {
    if (!content) return '';

    // 对于大型内容，可以考虑分块处理
    if (content.length > 50000) {
      // 只显示最后的50000个字符，避免DOM过大
      return '...(内容过长，显示最后部分)\n\n' + content.slice(-50000);
    }

    return content;
  }, [content]);

  // 内容统计信息
  const contentStats = useMemo(() => {
    if (!content) return { chars: 0, lines: 0, words: 0 };

    const chars = content.length;
    const lines = content.split('\n').length;
    const words = content.split(/\s+/).filter(word => word.length > 0).length;

    return { chars, lines, words };
  }, [content]);

  // 检测用户是否在滚动
  const handleScroll = useCallback(() => {
    if (!outputRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = outputRef.current;
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;

    setIsUserScrolling(!isAtBottom);
    setAutoScroll(isAtBottom);

    // 清除之前的定时器
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // 设置新的定时器，500ms后重新启用自动滚动
    scrollTimeoutRef.current = setTimeout(() => {
      setIsUserScrolling(false);
    }, 500);
  }, []);

  // 自动滚动到底部
  useEffect(() => {
    if (outputRef.current && autoScroll && !isUserScrolling && isStreaming) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [content, autoScroll, isUserScrolling, isStreaming]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // 复制内容到剪贴板
  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(content || '');
      setShowCopySuccess(true);
    } catch (err) {
      console.error('复制失败:', err);
    }
  }, [content]);

  // 切换展开状态
  const handleToggleExpand = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
      setAutoScroll(true);
      setIsUserScrolling(false);
    }
  }, []);

  // 如果没有内容且不在流式传输，显示骨架屏
  if (!content && !isStreaming) {
    return <StreamingSkeleton />;
  }

  return (
    <Paper elevation={3} sx={{ mb: 4, overflow: 'hidden' }}>
      {/* 头部控制区域 */}
      <Box sx={{
        p: 2,
        borderBottom: 1,
        borderColor: 'divider',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        bgcolor: 'grey.50'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="h6" component="h3">
            实时输出
          </Typography>

          {/* 内容统计 */}
          <Typography variant="caption" color="text.secondary">
            {contentStats.chars} 字符 | {contentStats.lines} 行 | {contentStats.words} 词
          </Typography>

          {/* 流式状态指示器 */}
          {isStreaming && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                bgcolor: isPaused ? 'warning.main' : 'success.main',
                animation: isPaused ? 'none' : 'pulse 1.5s infinite'
              }} />
              <Typography variant="caption" color="text.secondary">
                {isPaused ? '已暂停' : '生成中...'}
              </Typography>
            </Box>
          )}
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* 流式控制按钮 */}
          {isStreaming && onPause && onResume && (
            <IconButton
              size="small"
              onClick={isPaused ? onResume : onPause}
              title={isPaused ? '继续' : '暂停'}
            >
              {isPaused ? <PlayArrowIcon /> : <PauseIcon />}
            </IconButton>
          )}

          {/* 复制按钮 */}
          <IconButton
            size="small"
            onClick={handleCopy}
            disabled={!content}
            title="复制内容"
          >
            <ContentCopyIcon />
          </IconButton>

          {/* 展开/折叠按钮 */}
          <IconButton
            size="small"
            onClick={handleToggleExpand}
            title={isExpanded ? '折叠' : '展开'}
          >
            {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
          </IconButton>
        </Box>
      </Box>

      {/* 进度条 */}
      {showProgress && isStreaming && (
        <Box sx={{ px: 2 }}>
          <LinearProgress
            variant={isPaused ? "determinate" : "indeterminate"}
            value={isPaused ? 50 : undefined}
          />
        </Box>
      )}

      {/* 内容区域 */}
      <Collapse in={isExpanded}>
        <Box sx={{ position: 'relative' }}>
          <Box
            ref={outputRef}
            onScroll={handleScroll}
            sx={{
              backgroundColor: '#f8f9fa',
              p: 3,
              maxHeight: maxHeight,
              overflowY: 'auto',
              wordBreak: 'break-word',
              fontFamily: 'monospace',
              fontSize: '0.875rem',
              lineHeight: 1.6,
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                background: '#f1f1f1',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: '#c1c1c1',
                borderRadius: '4px',
                '&:hover': {
                  background: '#a8a8a8',
                },
              },
            }}
          >
            {processedContent ? (
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {processedContent}
              </ReactMarkdown>
            ) : (
              <Typography variant="body2" color="text.secondary">
                等待输出...
              </Typography>
            )}
          </Box>

          {/* 滚动到底部按钮 */}
          {isUserScrolling && isStreaming && (
            <Button
              variant="contained"
              size="small"
              onClick={scrollToBottom}
              sx={{
                position: 'absolute',
                bottom: 16,
                right: 16,
                minWidth: 'auto',
                px: 2,
                py: 1,
                fontSize: '0.75rem',
                zIndex: 1,
                boxShadow: 2,
              }}
            >
              滚动到底部
            </Button>
          )}
        </Box>
      </Collapse>

      {/* 复制成功提示 */}
      <Snackbar
        open={showCopySuccess}
        autoHideDuration={2000}
        onClose={() => setShowCopySuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setShowCopySuccess(false)}>
          内容已复制到剪贴板
        </Alert>
      </Snackbar>
    </Paper>
  );
});

StreamingOutput.displayName = 'StreamingOutput';

export default StreamingOutput;
