import React, { useState, useMemo, useCallback, useRef } from 'react';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Chip from '@mui/material/Chip';
import { FixedSizeList as List } from 'react-window';

// 优化的测试用例项组件
const TestCaseItem = React.memo(({ testCase, index, style, isExpanded, onToggle }) => {
  const getPriorityColor = useCallback((priority) => {
    switch (priority?.toLowerCase()) {
      case '高': return 'error';
      case '中': return 'warning';
      case '低': return 'success';
      default: return 'default';
    }
  }, []);

  const handleToggle = useCallback(() => {
    onToggle(index);
  }, [index, onToggle]);

  return (
    <div style={style}>
      <Paper
        elevation={1}
        sx={{
          m: 1,
          transition: 'all 0.2s ease-in-out',
          '&:hover': { elevation: 3 }
        }}
      >
        <Accordion expanded={isExpanded} onChange={handleToggle}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                {testCase.id || `TC-${index + 1}`}: {testCase.title}
              </Typography>
              {testCase.priority && (
                <Chip
                  label={testCase.priority}
                  color={getPriorityColor(testCase.priority)}
                  size="small"
                />
              )}
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Typography variant="body1">
                <strong>描述:</strong> {testCase.description}
              </Typography>

              {testCase.preconditions && (
                <Typography variant="body1">
                  <strong>前置条件:</strong> {testCase.preconditions}
                </Typography>
              )}

              <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                测试步骤
              </Typography>

              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell width="10%">#</TableCell>
                      <TableCell width="45%">步骤描述</TableCell>
                      <TableCell width="45%">预期结果</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {testCase.steps?.map((step) => (
                      <TableRow key={step.step_number}>
                        <TableCell>{step.step_number}</TableCell>
                        <TableCell>{step.description}</TableCell>
                        <TableCell>{step.expected_result}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          </AccordionDetails>
        </Accordion>
      </Paper>
    </div>
  );
});

TestCaseItem.displayName = 'TestCaseItem';

const TestCaseDisplay = ({ testCases = [], onExportToExcel }) => {
  const [expandedItems, setExpandedItems] = useState(new Set());
  const listRef = useRef(null);

  // 优化的切换展开状态函数
  const handleToggleExpand = useCallback((index) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  }, []);

  // 计算项目高度的函数
  const getItemSize = useCallback((index) => {
    const isExpanded = expandedItems.has(index);
    const testCase = testCases[index];

    if (!isExpanded) {
      return 80; // 折叠状态的基础高度
    }

    // 展开状态的动态高度计算
    const baseHeight = 200; // 基础展开高度
    const stepHeight = 40; // 每个步骤的高度
    const stepsCount = testCase?.steps?.length || 0;
    const preconditionHeight = testCase?.preconditions ? 30 : 0;

    return baseHeight + (stepsCount * stepHeight) + preconditionHeight;
  }, [expandedItems, testCases]);

  // 生成Markdown格式的测试用例（用于导出）
  const generateMarkdown = useMemo(() => {
    if (!testCases || testCases.length === 0) return '';

    let markdown = '# 生成的测试用例\n\n';

    testCases.forEach((testCase, index) => {
      markdown += `## ${testCase.id || `TC-${index + 1}`}: ${testCase.title}\n\n`;

      if (testCase.priority) {
        markdown += `**优先级:** ${testCase.priority}\n\n`;
      }

      markdown += `**描述:** ${testCase.description}\n\n`;

      if (testCase.preconditions) {
        markdown += `**前置条件:** ${testCase.preconditions}\n\n`;
      }

      markdown += `### 测试步骤\n\n`;
      markdown += `| # | 步骤描述 | 预期结果 |\n`;
      markdown += `| --- | --- | --- |\n`;

      testCase.steps?.forEach(step => {
        markdown += `| ${step.step_number} | ${step.description} | ${step.expected_result} |\n`;
      });

      markdown += '\n\n';
    });

    return markdown;
  }, [testCases]);

  // 渲染虚拟列表项
  const renderItem = useCallback(({ index, style }) => (
    <TestCaseItem
      testCase={testCases[index]}
      index={index}
      style={style}
      isExpanded={expandedItems.has(index)}
      onToggle={handleToggleExpand}
    />
  ), [testCases, expandedItems, handleToggleExpand]);

  // 如果测试用例数量较少，使用普通渲染
  if (testCases.length <= 10) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            已生成 {testCases.length} 个测试用例
          </Typography>
          {onExportToExcel && (
            <Button
              variant="outlined"
              startIcon={<FileDownloadIcon />}
              onClick={() => onExportToExcel(testCases)}
              size="small"
            >
              导出Excel
            </Button>
          )}
        </Box>

        <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
          {testCases.map((testCase, index) => (
            <TestCaseItem
              key={testCase.id || index}
              testCase={testCase}
              index={index}
              style={{}}
              isExpanded={expandedItems.has(index)}
              onToggle={handleToggleExpand}
            />
          ))}
        </Box>
      </Box>
    );
  }

  // 大量测试用例使用虚拟滚动
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="body1" color="text.secondary">
          已生成 {testCases.length} 个测试用例 (虚拟滚动模式)
        </Typography>
        {onExportToExcel && (
          <Button
            variant="outlined"
            startIcon={<FileDownloadIcon />}
            onClick={() => onExportToExcel(testCases)}
            size="small"
          >
            导出Excel
          </Button>
        )}
      </Box>

      <Box sx={{ flexGrow: 1, height: '600px' }}>
        <List
          ref={listRef}
          height={600}
          itemCount={testCases.length}
          itemSize={getItemSize}
          overscanCount={5}
        >
          {renderItem}
        </List>
      </Box>
    </Box>
  );
};

export default TestCaseDisplay;
