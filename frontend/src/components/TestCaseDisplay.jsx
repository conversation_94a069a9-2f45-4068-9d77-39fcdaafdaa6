import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { VariableSizeList as List } from 'react-window';
import Paper from '@mui/material/Paper';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Chip from '@mui/material/Chip';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import Skeleton from '@mui/material/Skeleton';

// 单个测试用例项组件 - 使用React.memo优化
const TestCaseItem = React.memo(({ testCase, index, style, onHeightChange }) => {
  const [expanded, setExpanded] = useState(false);
  const itemRef = useRef(null);
  const resizeObserverRef = useRef(null);

  // 计算并报告高度的函数
  const measureAndReportHeight = useCallback(() => {
    if (itemRef.current && onHeightChange) {
      const height = itemRef.current.offsetHeight;
      onHeightChange(index, height);
    }
  }, [index, onHeightChange]);

  // 处理展开/收起
  const handleToggle = useCallback(() => {
    setExpanded(prev => {
      const newExpanded = !prev;

      // 使用 requestAnimationFrame 确保在下一帧测量
      requestAnimationFrame(() => {
        measureAndReportHeight();
        // 多次测量确保捕获到动画的各个阶段
        setTimeout(measureAndReportHeight, 100);
        setTimeout(measureAndReportHeight, 200);
        setTimeout(measureAndReportHeight, 350); // Material-UI Accordion 动画通常是 300ms
        setTimeout(measureAndReportHeight, 500); // 额外的安全边际
      });

      return newExpanded;
    });
  }, [measureAndReportHeight]);

  // 使用 ResizeObserver 监听高度变化
  useEffect(() => {
    if (itemRef.current) {
      resizeObserverRef.current = new ResizeObserver(() => {
        measureAndReportHeight();
      });
      resizeObserverRef.current.observe(itemRef.current);
    }

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, [measureAndReportHeight]);

  // 初始化时计算高度
  useEffect(() => {
    measureAndReportHeight();
  }, [measureAndReportHeight]);

  return (
    <div style={style}>
      <div ref={itemRef}>
        <Box sx={{ p: 1, mx: 1 }}>
          <Accordion
            expanded={expanded}
            onChange={handleToggle}
            slotProps={{
              transition: {
                timeout: 300,
                onEntered: measureAndReportHeight,
                onExited: measureAndReportHeight,
              }
            }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls={`panel${index}-content`}
              id={`panel${index}-header`}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  {testCase.id || `TC-${index + 1}`}: {testCase.title}
                </Typography>
                {testCase.priority && (
                  <Chip
                    label={testCase.priority}
                    size="small"
                    color={testCase.priority === '高' ? 'error' : testCase.priority === '中' ? 'warning' : 'default'}
                  />
                )}
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  <strong>描述:</strong> {testCase.description}
                </Typography>

                {testCase.preconditions && (
                  <Typography variant="body2" color="text.secondary">
                    <strong>前置条件:</strong> {testCase.preconditions}
                  </Typography>
                )}

                {testCase.steps && testCase.steps.length > 0 && (
                  <TableContainer component={Paper} elevation={1}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell><strong>#</strong></TableCell>
                          <TableCell><strong>步骤描述</strong></TableCell>
                          <TableCell><strong>预期结果</strong></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {testCase.steps.map((step, stepIndex) => (
                          <TableRow key={stepIndex}>
                            <TableCell>{step.step_number}</TableCell>
                            <TableCell>{step.description}</TableCell>
                            <TableCell>{step.expected_result}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </Box>
            </AccordionDetails>
          </Accordion>
        </Box>
      </div>
    </div>
  );
});

// 骨架屏组件
const TestCaseSkeleton = React.memo(() => (
  <Box sx={{ p: 1, mx: 1 }}>
    <Paper elevation={1} sx={{ p: 2 }}>
      <Skeleton variant="text" width="60%" height={32} />
      <Skeleton variant="text" width="40%" height={20} sx={{ mt: 1 }} />
      <Skeleton variant="rectangular" width="100%" height={60} sx={{ mt: 2 }} />
    </Paper>
  </Box>
));

const TestCaseDisplay = ({ testCases = [] }) => {
  const [isLoading] = useState(false);
  const listRef = useRef(null);
  const itemHeights = useRef({});

  // 使用useMemo缓存计算结果
  const memoizedTestCases = useMemo(() => testCases, [testCases]);

  // 处理高度变化
  const handleHeightChange = useCallback((index, height) => {
    const currentHeight = itemHeights.current[index];
    if (currentHeight !== height && height > 0) {
      console.log(`Height changed for item ${index}: ${currentHeight} -> ${height}`);
      itemHeights.current[index] = height;
      // 立即重新计算虚拟滚动的布局
      if (listRef.current) {
        listRef.current.resetAfterIndex(index);
      }
    }
  }, []);

  // 获取项目高度
  const getItemSize = useCallback((index) => {
    const height = itemHeights.current[index];
    return height && height > 0 ? height : 120; // 默认高度120px
  }, []);

  // 虚拟滚动的行渲染器
  const Row = useCallback(({ index, style }) => {
    if (isLoading) {
      return <TestCaseSkeleton />;
    }

    const testCase = memoizedTestCases[index];
    if (!testCase) {
      return <TestCaseSkeleton />;
    }

    return (
      <TestCaseItem
        testCase={testCase}
        index={index}
        style={style}
        onHeightChange={handleHeightChange}
      />
    );
  }, [memoizedTestCases, isLoading, handleHeightChange]);

  // 生成Markdown格式的测试用例（保持原有功能）
  const generateMarkdown = useMemo(() => {
    if (!memoizedTestCases || memoizedTestCases.length === 0) return '';

    let markdown = '# 生成的测试用例\n\n';

    memoizedTestCases.forEach((testCase, index) => {
      markdown += `## ${testCase.id || `TC-${index + 1}`}: ${testCase.title}\n\n`;

      if (testCase.priority) {
        markdown += `**优先级:** ${testCase.priority}\n\n`;
      }

      markdown += `**描述:** ${testCase.description}\n\n`;

      if (testCase.preconditions) {
        markdown += `**前置条件:** ${testCase.preconditions}\n\n`;
      }

      markdown += `### 测试步骤\n\n`;
      markdown += `| # | 步骤描述 | 预期结果 |\n`;
      markdown += `| --- | --- | --- |\n`;

      testCase.steps.forEach(step => {
        markdown += `| ${step.step_number} | ${step.description} | ${step.expected_result} |\n`;
      });

      markdown += '\n\n';
    });

    return markdown;
  }, [memoizedTestCases]);

  // 决定使用哪种渲染方式
  const shouldUseVirtualScroll = memoizedTestCases.length > 10;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Box sx={{ mb: 2 }}>
        <Typography variant="body1" color="text.secondary">
          已生成 {memoizedTestCases.length} 个测试用例
          {shouldUseVirtualScroll && (
            <Typography variant="caption" display="block" color="text.secondary">
              使用虚拟滚动优化性能
            </Typography>
          )}
        </Typography>
      </Box>

      {shouldUseVirtualScroll ? (
        // 大量数据时使用虚拟滚动
        <Box sx={{
          flexGrow: 1,
          bgcolor: '#f8f9fa',
          borderRadius: 1,
          overflow: 'hidden'
        }}>
          <List
            ref={listRef}
            height={600}
            itemCount={memoizedTestCases.length}
            itemSize={getItemSize}
            width="100%"
            estimatedItemSize={120}
          >
            {Row}
          </List>
        </Box>
      ) : (
        // 少量数据时使用原有的Markdown渲染
        <Box sx={{
          flexGrow: 1,
          overflow: 'auto',
          bgcolor: '#f8f9fa',
          p: 3,
          borderRadius: 1,
          maxHeight: '600px'
        }}>
          <ReactMarkdown remarkPlugins={[remarkGfm]}>
            {generateMarkdown}
          </ReactMarkdown>
        </Box>
      )}
    </Box>
  );
};

export default TestCaseDisplay;
