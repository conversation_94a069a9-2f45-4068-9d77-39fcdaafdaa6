/* 性能优化的动画样式 */

/* 脉冲动画 - 用于流式输出状态指示器 */
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 淡入动画 - 用于组件加载 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滑入动画 - 用于侧边栏或抽屉 */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* 骨架屏闪烁动画 */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 加载旋转动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 弹跳动画 - 用于按钮点击反馈 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* 缩放动画 - 用于卡片悬停 */
@keyframes scaleUp {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.02);
  }
}

/* 进度条动画 */
@keyframes progressBar {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

/* 通用动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.pulse {
  animation: pulse 1.5s infinite;
}

.rotating {
  animation: rotate 2s linear infinite;
}

.bounce {
  animation: bounce 0.6s ease-out;
}

.scale-up {
  animation: scaleUp 0.2s ease-out;
}

/* 骨架屏样式 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* 性能优化的过渡效果 */
.smooth-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

.smooth-transition-slow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

/* 虚拟滚动优化 */
.virtual-scroll-container {
  contain: layout style paint;
  transform: translateZ(0);
}

.virtual-scroll-item {
  contain: layout style paint;
  will-change: transform;
}

/* 减少重绘的优化 */
.optimized-card {
  contain: layout style paint;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 响应式动画 - 减少动画在低性能设备上的影响 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高性能设备的增强动画 */
@media (min-width: 1024px) and (min-height: 768px) {
  .enhanced-animation {
    animation-duration: 0.4s;
    transition-duration: 0.3s;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .hover-effect:hover {
    transform: none;
  }
  
  .touch-feedback:active {
    transform: scale(0.98);
    transition: transform 0.1s ease-out;
  }
}

/* 深色模式动画优化 */
@media (prefers-color-scheme: dark) {
  .skeleton {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }
}

/* 滚动条样式优化 */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载状态优化 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
  z-index: 1000;
}

.loading-spinner {
  animation: rotate 1s linear infinite;
}

/* 错误状态动画 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

.error-shake {
  animation: shake 0.5s ease-in-out;
}

/* 成功状态动画 */
@keyframes checkmark {
  0% {
    stroke-dashoffset: 100;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

.success-checkmark {
  stroke-dasharray: 100;
  animation: checkmark 0.6s ease-in-out;
}
